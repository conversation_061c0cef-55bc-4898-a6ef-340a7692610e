{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [


        {
            "name": "Debug Angular App",
            "type": "chrome",
            "request": "launch",
            "preLaunchTask": "Start Angular",
            "url": "http://localhost:4200",
            "webRoot": "${workspaceFolder}/myapp",
            "sourceMaps": true,
            "sourceMapPathOverrides": {
                "webpack:///./src/*": "${webRoot}/src/*",
                "webpack:///src/*": "${webRoot}/src/*",
                "webpack:///*": "${webRoot}/*",
                "webpack:///./~/*": "${webRoot}/node_modules/*"
            },
            "runtimeArgs": [
                "--disable-web-security",
                "--user-data-dir=${workspaceFolder}/.chrome-debug-profile"
            ]
        }
    ]
}
