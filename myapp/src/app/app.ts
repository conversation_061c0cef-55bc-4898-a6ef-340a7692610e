  import { Component, inject } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { ProductList } from './product-list/product-list';import { Copyright } from './directives/copyright';
import { KeyLogger } from './key-logger/key-logger';
import { APP_SETTINGS } from './app.settings';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, ProductList, Copyright, KeyLogger],
  templateUrl: './app.html',
  styleUrls: ['./app.css'],
})
export class App {
  readonly settings = inject(APP_SETTINGS);
}
