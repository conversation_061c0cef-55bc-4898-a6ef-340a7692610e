import { inject, Injectable } from '@angular/core';
import { Product } from '../product';
import { Observable, of, tap } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { APP_SETTINGS } from '../app.settings';

@Injectable({
  providedIn: 'root'
})
export class Products {

  constructor() { }

  private http = inject(HttpClient);

  private productsUrl = inject(APP_SETTINGS).apiUrl + '/products';



  getProducts(): Observable<Product[]> {
    return this.http.get<Product[]>(this.productsUrl);
  }
}
