import { Component, Host, Input, Optional } from '@angular/core';
import { Product } from '../product';
import { ProductView as ProductViewService } from '../services/product-view';

@Component({
  selector: 'app-product-view',
  imports: [],
  templateUrl: './product-view.html',
  styleUrl: './product-view.css',
  providers: [ProductViewService]
})
export class ProductView {
  @Input() id: number = 0;
  product: Product | null = null;
  constructor(@Host() @Optional() private productView: ProductViewService) { }

  ngOnInit(): void {
    this.product = this.productView.getProduct(this.id);
  }
}
