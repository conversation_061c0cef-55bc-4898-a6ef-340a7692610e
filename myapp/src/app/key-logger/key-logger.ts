import { Component, ElementRef, input, OnInit, ViewChild } from '@angular/core';
import { filter, fromEvent, map, tap } from 'rxjs';

@Component({
  selector: 'app-key-logger',
  imports: [],
  templateUrl: './key-logger.html',
  styleUrl: './key-logger.css'
})
export class KeyLogger implements OnInit {
  
  @ViewChild('keyContainer') input!: ElementRef;
  keys = '';
  numeric = input(false);

  ngOnInit(): void {
    const logger$ = fromEvent<KeyboardEvent>(this.input!.nativeElement, 'keyup');
    logger$.pipe(
      map(evt => evt.key.charCodeAt(0)),
      filter(code => {
        if (this.numeric()) {
          return (code > 31 && (code < 48 || code > 57)) === false;
        }
        return true;
      }),
      tap(digit => this.keys += String.fromCharCode(digit))
    ).subscribe();
  }
  
}
