import { ChangeDetectionStrategy, Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Product } from '../product';

@Component({
  selector: 'app-product-detail',
  imports: [CommonModule],
  templateUrl: './product-detail.html',
  styleUrl: './product-detail.css',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProductDetail implements OnInit, OnChanges {
  @Input() product!: Product;
  @Output() addedToCart = new EventEmitter<Product>();

  constructor() {
  }
  ngOnChanges(changes: SimpleChanges): void {
    const product = changes['product'];
    if (product && !product.isFirstChange()) {
      console.log('Product changed:', product.currentValue);
    }
  }
  ngOnInit(): void {
  }

  addToCart(): void {
    this.addedToCart.emit(this.product);
  }

  getProductTitle(): string {
    return this.product.title;
  }
}
