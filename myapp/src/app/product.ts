export interface Product {
    id: number;
    title: string;
    price: number;
    // Option 6: Record with number keys (currently active)
    categories: Record<number, string>;

    // Option 1: Array of strings
    // categories: string[];

    // Option 2: Array of category objects (uncomment to use)
    // categories: { id: number; name: string; }[];

    // Option 3: Simple string (single category)
    // categories: string;

    // Option 4: Set of strings
    // categories: Set<string>;

    // Option 5: Map with number keys
    // categories: Map<string, string>;
}
