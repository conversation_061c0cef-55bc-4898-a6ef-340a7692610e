@let products = productsService.products;
@if (products) {
  <h1>Products ({{products.length}})</h1>
}
<ul class="pill-group" aria-label="Product List" role="listbox" tabindex="0">
  @for (product of products | sort; track product.id) {
    <li class="pill" role="option" tabindex="0" (click)="onProductSelected(product)">
      <app-product-view [id]="product.id"></app-product-view>
    </li>
  } @empty {
    <p>No products found.</p>
  }
</ul>
@if (selectedProduct) {
  <app-product-detail [product]="selectedProduct" (addedToCart)="onProductAddedToCart($any($event))"></app-product-detail>
}
<h1>Favorites</h1>
<app-favorites></app-favorites>