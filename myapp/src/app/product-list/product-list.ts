import { AfterViewInit, Component, DestroyRef, inject, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { Product } from '../product';
import { ProductDetail } from '../product-detail/product-detail';
import { SortPipe } from '../pipe/sort-pipe';
import { Products } from '../services/products';
import { Favorite } from '../favorite/favorite';
import { ProductView } from '../product-view/product-view';
import { async, Observable, Subscription } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-product-list',
  imports: [ProductDetail, SortPipe, Favorite, ProductView],
  templateUrl: './product-list.html',
  styleUrls: ['./product-list.css'],
  providers: [Products]
})
export class ProductList implements AfterViewInit {
  constructor(private productsService: Products) { }

  private destroyRef = inject(DestroyRef);
  products$: Observable<Product[]> | undefined;
  @ViewChild(ProductDetail) productDetail!: ProductDetail;

  ngOnInit(): void {
    this.getProducts();
  }

  ngAfterViewInit(): void {
    if (this.productDetail) {
      console.log(this.productDetail.product);
    }
  }

  onProductSelected(product: Product): void {
    this.selectedProduct = product;
  }

  onProductAddedToCart(product: Product): void {
    alert(`${product.title} added to cart.`);
  }

  private getProducts() {
    this.products$ = this.productsService.getProducts()
  } 
}
