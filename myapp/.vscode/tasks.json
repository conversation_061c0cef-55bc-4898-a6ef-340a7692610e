{
  // For more information, visit: https://go.microsoft.com/fwlink/?LinkId=733558
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Start Angular",
      "type": "shell",
      "command": "cd myapp && npm start",
      "isBackground": true,
      "problemMatcher": {
        "owner": "typescript",
        "pattern": "$tsc",
        "background": {
          "activeOnStart": true,
          "beginsPattern": ".*",
          "endsPattern": "Compiled successfully"
        }
      }
    }
  ]
}
